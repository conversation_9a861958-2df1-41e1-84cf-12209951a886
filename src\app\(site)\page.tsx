
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Download, Github, Linkedin, User, Lightbulb, Briefcase, Mail, Code, FileText, type LucideIcon, Menu, X, Sun, <PERSON>, Lapt<PERSON> } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ProjectCard } from '@/components/shared/ProjectCard'; // Assuming this is a well-styled component
import { getProjects } from '@/actions/projectActions';
import { getSkills } from '@/actions/skillActions';
import { getAboutData } from '@/actions/aboutActions';
import { getExperience } from '@/actions/experienceActions';
import { getCurrentCvInfo } from '@/actions/cvActions';
import * as LucideIcons from 'lucide-react';

// Helper component for section titles
const SectionTitle = ({ title, icon: Icon }: { title: string; icon?: LucideIcon }) => (
  <div className="flex items-center justify-center mb-12">
    {Icon && <Icon className="h-10 w-10 text-primary mr-4" />}
    <h2 className="text-4xl md:text-5xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
      {title}
    </h2>
  </div>
);

export default async function HomePage() {
  const projects = await getProjects();
  const featuredProjects = projects.slice(0, 3); // Show 3 featured projects

  const skills = await getSkills();
  const keySkills = skills.slice(0, 6); // Show 6 key skills

  const aboutData = await getAboutData();
  const experience = await getExperience();
  const recentExperience = experience.length > 0 ? experience[0] : null;

  const cvInfo = await getCurrentCvInfo();
  const isLocalProfilePic = aboutData.profilePictureUrl?.startsWith('/');

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col antialiased">
      {/* Hero Section */}
      <section 
        id="home"
        className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-background via-muted/30 to-background py-4 px-4 sm:px-6 lg:px-8"
      >
        <div className="absolute inset-0 opacity-10">
          {/* Subtle background pattern or animation could go here */}
        </div>
        <div className="container mx-auto max-w-6xl text-center z-10">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="md:text-left">
              <span className="text-3xl font-semibold uppercase tracking-wider text-primary mb-2 block">Hi, I'm {aboutData.fullName || 'Your Name'}</span>
              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-extrabold tracking-tight mb-6">
                <span className="block">Creative Developer</span>
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">UI/UX Enthusiast</span>
              </h1>
              <p className="mt-6 max-w-xl text-lg sm:text-xl text-muted-foreground md:mx-0 mx-auto mb-10">
                {aboutData.bioParagraphs?.[0] || "I craft beautiful, engaging, and accessible digital experiences. Passionate about clean code, intuitive design, and pushing creative boundaries to build solutions that matter."}
              </p>
              <div className="flex flex-col sm:flex-row sm:justify-center md:justify-start gap-4 mb-10">
                <Button size="lg" asChild className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Link href="#contact">
                    Contact Me <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild className="border-primary text-primary hover:bg-primary/10 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Link href="#projects">
                    View My Work
                  </Link>
                </Button>
              </div>
              <div className="flex justify-center md:justify-start space-x-6 items-center">
                {aboutData.socialLinks?.slice(0, 3).map(link => {
                  const IconComponent = (link.iconName && LucideIcons[link.iconName as keyof typeof LucideIcons]) ? LucideIcons[link.iconName as keyof typeof LucideIcons] as LucideIcon : Github;
                  return (
                    <Link key={link.id} href={link.url} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors duration-300 transform hover:scale-110">
                      <IconComponent className="h-7 w-7" />
                    </Link>
                  );
                })}
                {cvInfo?.fileName && (
                  <Button variant="link" asChild className="text-primary hover:text-primary/80 p-0 h-auto text-base font-medium">
                    <Link href={`/${cvInfo.fileName}`} download={`${(aboutData.fullName || 'User').replace(/\s+/g, '_')}_CV.pdf`} target="_blank" rel="noopener noreferrer">
                      <Download className="mr-2 h-5 w-5" /> Download CV
                    </Link>
                  </Button>
                )}
              </div>
            </div>
            <div className="relative group flex justify-center items-center mt-10 md:mt-0">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-accent rounded-full blur-xl opacity-50 group-hover:opacity-75 transition duration-1000 group-hover:duration-200 animate-tilt"></div>
              <Image
                src={aboutData.profilePictureUrl || "https://placehold.co/600x600/333/fff.png?text=Me"}
                alt={`Profile of ${aboutData.fullName || 'User'}`}
                width={400}
                height={400}
                className="rounded-full shadow-2xl object-cover aspect-square relative z-10 border-4 border-background transform group-hover:scale-105 transition-transform duration-300"
                priority
                unoptimized={!isLocalProfilePic && aboutData.profilePictureUrl?.includes('placehold.co')}
              />
            </div>
          </div>
        </div>
      </section>

      {/* About Me Section */}
      <section id="about" className="py-4 md:py-4 bg-muted/50">
        <div className="container mx-auto max-w-5xl px-4 sm:px-6 lg:px-8">
          <SectionTitle title="About Me" icon={User} />
          <Card className="shadow-2xl overflow-hidden bg-card border-2 border-transparent hover:border-primary/30 transition-all duration-500 transform hover:-translate-y-2">
            <div className="md:flex flex items-center justify-center">
             
              <div className="md:w-2/3">
                <CardHeader className="p-6 md:p-8">
                  <CardTitle className="text-3xl font-semibold text-primary leading-tight mb-2">
                    A Little More About My Journey
                  </CardTitle>
                  <CardDescription className="text-lg text-muted-foreground">
                    Developer, Designer, Dreamer.
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6 md:p-8 pt-0 md:pt-0 space-y-5 text-base text-muted-foreground leading-relaxed">
                  <p>
                    {aboutData.bioParagraphs?.[1] || "I'm a dedicated developer passionate about crafting impactful digital solutions and exploring new technologies. I thrive in collaborative environments, turning complex challenges into elegant, user-friendly experiences."}
                  </p>
                  <p>
                    {aboutData.bioParagraphs?.[2] || "My approach combines technical expertise with a keen eye for design, ensuring that every project is not only functional but also aesthetically pleasing and intuitive to use. I'm always eager to learn and grow, embracing new challenges as opportunities to expand my skillset."}
                  </p>
                  <div className="pt-4">
                    <Button asChild variant="ghost" className="text-primary hover:bg-primary/10 hover:text-primary/90 font-semibold">
                      <Link href="/about"> {/* Assuming you have a dedicated about page */}
                        Discover Full Story <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Skills Section */}
      {keySkills.length > 0 && (
        <section id="skills" className="py-4 md:py-4 bg-background">
          <div className="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
            <SectionTitle title="My Tech Arsenal" icon={Lightbulb} />
            <p className="text-lg text-center text-muted-foreground mb-16 max-w-3xl mx-auto">
              I'm proficient in a range of modern web technologies, always eager to learn and adapt. Here are some of the tools and technologies I love working with:
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 md:gap-8 mb-16">
              {keySkills.map((skill) => {
                const IconComponent = (skill.iconName && LucideIcons[skill.iconName as keyof typeof LucideIcons]) ? LucideIcons[skill.iconName as keyof typeof LucideIcons] as LucideIcon : Code;
                return (
                  <Card key={skill.id} className="group p-6 bg-card border border-border hover:border-primary/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col items-center justify-center text-center aspect-square">
                    <IconComponent className="h-12 w-12 text-primary mb-4 transition-transform duration-300 group-hover:scale-110" />
                    <p className="font-semibold text-foreground text-sm md:text-base">{skill.name}</p>
                  </Card>
                );
              })}
            </div>
            <div className="text-center">
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                <Link href="/skills"> {/* Assuming you have a dedicated skills page */}
                  Explore All My Skills <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Featured Projects Section */}
      {featuredProjects.length > 0 && (
        <section id="projects" className="py-5 md:py-5 bg-muted/50">
          <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <SectionTitle title="Featured Creations" icon={Briefcase} />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10 mb-6">
              {featuredProjects.map((project) => (
                <ProjectCard key={project.id} project={project} /> // Ensure ProjectCard is well-styled
              ))}
            </div>
            <div className="text-center">
              <Button asChild size="lg" variant="outline" className="border-primary text-primary hover:bg-primary/10 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                <Link href="/projects"> {/* Assuming you have a dedicated projects page */}
                  View All Projects <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Experience Highlights Section */}
      {recentExperience && (
        <section id="experience" className="py-5 md:py-5 bg-background">
          <div className="container mx-auto max-w-5xl px-4 sm:px-6 lg:px-8">
            <SectionTitle title="Professional Journey" icon={FileText} />
            <Card className="shadow-xl overflow-hidden bg-card border-2 border-transparent hover:border-accent/30 transition-all duration-500 transform hover:-translate-y-2">
              <CardHeader className="bg-gradient-to-r from-accent/10 to-primary/10 p-6 md:p-8">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/20 rounded-full">
                     <FileText className="h-8 w-8 text-primary flex-shrink-0" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl md:text-3xl font-semibold text-primary leading-tight">
                      My Recent Role
                    </CardTitle>
                    <CardDescription className="text-md text-muted-foreground">
                      At {recentExperience.companyName} as a {recentExperience.role}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6 md:p-8 space-y-4">
                <p className="text-base md:text-lg text-muted-foreground leading-relaxed">
                  {recentExperience.description?.[0] ? `${recentExperience.description[0].charAt(0).toUpperCase() + recentExperience.description[0].slice(1)}` : "Engaged in challenging and rewarding projects, contributing to the company's success through innovative solutions and teamwork."}
                </p>
                <div className="pt-3">
                  <Button asChild variant="ghost" className="text-primary hover:bg-primary/10 hover:text-primary/90 font-semibold">
                    <Link href="/experience"> {/* Assuming you have a dedicated experience page */}
                      See My Full Experience <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section id="contact" className="py-20 md:py-32 bg-gradient-to-br from-primary to-accent text-primary-foreground">
        <div className="container mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 text-center">
          <Mail className="h-16 w-16 mx-auto mb-6 text-background/80" />
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Let's Connect!</h2>
          <p className="text-lg md:text-xl mb-10 opacity-90 max-w-2xl mx-auto">
            Have a project in mind, a question, or just want to say hi? I'd love to hear from you. Feel free to reach out!
          </p>
          <Button size="lg" variant="secondary" asChild className="bg-background text-primary hover:bg-background/90 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 py-3 px-8 text-lg">
            <Link href="/contact"> {/* Assuming you have a dedicated contact page */}
              Get In Touch <ArrowRight className="ml-3 h-6 w-6" />
            </Link>
          </Button>
        </div>
      </section>

      
    </div>
  );
}


